import React from 'react';
import { useTranslation } from 'react-i18next';
import { RangePicker, Select, ResponsiveGrid } from '@/shared/components/common';

export interface ChartFilterData {
  /**
   * <PERSON><PERSON><PERSON>ng thời gian [startDate, endDate]
   */
  dateRange: [Date | null, Date | null];
  
  /**
   * Loại dữ liệu: commission, orders, customers
   */
  dataType: string;
}

export interface ChartFilterProps {
  /**
   * Giá trị filter hiện tại
   */
  value: ChartFilterData;
  
  /**
   * Callback khi filter thay đổi
   */
  onChange: (filter: ChartFilterData) => void;
  
  /**
   * Class CSS bổ sung
   */
  className?: string;
}

/**
 * Component bộ lọc cho biểu đồ affiliate
 */
const ChartFilter: React.FC<ChartFilterProps> = ({
  value,
  onChange,
  className = '',
}) => {
  const { t } = useTranslation(['userAffiliate']);

  // Options cho loại dữ liệu
  const dataTypeOptions = [
    {
      value: 'commission',
      label: t('userAffiliate:chartFilter.dataTypes.commission'),
    },
    {
      value: 'orders',
      label: t('userAffiliate:chartFilter.dataTypes.orders'),
    },
    {
      value: 'customers',
      label: t('userAffiliate:chartFilter.dataTypes.customers'),
    },
  ];

  const handleDateRangeChange = (dateRange: [Date | null, Date | null]) => {
    onChange({
      ...value,
      dateRange,
    });
  };

  const handleDataTypeChange = (dataType: string | string[] | number | number[]) => {
    onChange({
      ...value,
      dataType: dataType as string,
    });
  };

  return (
    <div className={`mb-4 ${className}`}>
      <ResponsiveGrid maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 2 }} gap={4}>
        <div>
          <RangePicker
            label={t('userAffiliate:chartFilter.timeRange')}
            value={value.dateRange}
            onChange={handleDateRangeChange}
            placeholder={[
              t('userAffiliate:chartFilter.selectTimeRange'),
              t('userAffiliate:chartFilter.selectTimeRange')
            ]}
            fullWidth
            clearable
          />
        </div>
        
        <div>
          <Select
            label={t('userAffiliate:chartFilter.dataType')}
            value={value.dataType}
            onChange={handleDataTypeChange}
            options={dataTypeOptions}
            placeholder={t('userAffiliate:chartFilter.selectDataType')}
            fullWidth
          />
        </div>
      </ResponsiveGrid>
    </div>
  );
};

export default ChartFilter;
